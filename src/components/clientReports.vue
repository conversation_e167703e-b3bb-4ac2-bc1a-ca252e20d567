<template>
  <div class="grid">
    <div class="col-12">
      <div class="card">
        <div class="flex flex-wrap align-items-end gap-4 mb-4">
          <div class="field mr-3">
            <label for="startDate" class="block text-sm font-medium mb-1"
              >Start Date</label
            >
            <Calendar
              v-model="startDate"
              dateFormat="dd/mm/yy"
              inputId="startDate"
              :showIcon="true"
              class="w-9rem"
            />
          </div>
          <div class="field mr-3">
            <label for="endDate" class="block text-sm font-medium mb-1"
              >End Date</label
            >
            <Calendar
              v-model="endDate"
              dateFormat="dd/mm/yy"
              inputId="endDate"
              :showIcon="true"
              class="w-9rem"
            />
          </div>
          <div class="field mr-3">
            <label for="clientEmail" class="block text-sm font-medium mb-1"
              >Client Email (Optional)</label
            >
            <Dropdown
              id="clientEmail"
              v-model="selectedClient"
              :options="clients"
              optionLabel="email"
              placeholder="Select client"
              :filter="true"
              :showClear="true"
              class="w-12rem"
            />
          </div>
          <div class="field mr-4">
            <Button
              label="Generate"
              icon="pi pi-search"
              class="p-button-primary p-button-sm"
              @click="generateReport"
              :loading="loading"
            />
          </div>
          <div class="field ml-2" v-if="reportData">
            <span class="block text-sm font-medium mb-1">Downloads:</span>
            <div class="flex gap-3">
              <a
                href="#"
                @click.prevent="downloadRacingAustraliaCSV"
                class="text-primary text-sm hover:underline"
              >
                <i class="pi pi-download mr-1"></i>Racing Australia
              </a>
              <span class="text-300 mx-1">|</span>
              <a
                href="#"
                @click.prevent="downloadMedialityRacingCSV"
                class="text-primary text-sm hover:underline"
              >
                <i class="pi pi-download mr-1"></i>Mediality Racing
              </a>
            </div>
          </div>
        </div>

        <TabView v-if="reportData">
          <TabPanel header="Summary">
            <!-- Overall Summary -->
            <div class="grid">
              <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                  <div class="flex justify-content-between mb-3">
                    <div>
                      <span class="block text-500 font-medium mb-3"
                        >Total Clients</span
                      >
                      <div class="text-900 font-medium text-xl">
                        {{ reportData.summary?.total_clients || 0 }}
                      </div>
                    </div>
                    <div
                      class="flex align-items-center justify-content-center bg-blue-100 border-round"
                      style="width: 2.5rem; height: 2.5rem"
                    >
                      <i class="pi pi-users text-blue-500 text-xl"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                  <div class="flex justify-content-between mb-3">
                    <div>
                      <span class="block text-500 font-medium mb-3"
                        >Total Price ID Count</span
                      >
                      <div class="text-900 font-medium text-xl">
                        {{ reportData.summary?.total_price_id_count || 0 }}
                      </div>
                    </div>
                    <div
                      class="flex align-items-center justify-content-center bg-green-100 border-round"
                      style="width: 2.5rem; height: 2.5rem"
                    >
                      <i class="pi pi-dollar text-green-500 text-xl"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                  <div class="flex justify-content-between mb-3">
                    <div>
                      <span class="block text-500 font-medium mb-3"
                        >Racing Australia Usage</span
                      >
                      <div class="text-900 font-medium text-xl">
                        {{ racingAustraliaUsage }}
                      </div>
                    </div>
                    <div
                      class="flex align-items-center justify-content-center bg-orange-100 border-round"
                      style="width: 2.5rem; height: 2.5rem"
                    >
                      <i class="pi pi-flag text-orange-500 text-xl"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                  <div class="flex justify-content-between mb-3">
                    <div>
                      <span class="block text-500 font-medium mb-3"
                        >Mediality Racing Usage</span
                      >
                      <div class="text-900 font-medium text-xl">
                        {{ medialityRacingUsage }}
                      </div>
                    </div>
                    <div
                      class="flex align-items-center justify-content-center bg-purple-100 border-round"
                      style="width: 2.5rem; height: 2.5rem"
                    >
                      <i class="pi pi-video text-purple-500 text-xl"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Client Summary Report -->
            <div class="card mt-4">
              <h5>Client Summary {{ formatDateRange() }}</h5>

              <div
                v-for="client in processedClientData"
                :key="client.clientEmail"
                class="mb-6"
              >
                <div
                  class="grid align-items-center mb-3 p-3 surface-100 border-round"
                >
                  <div class="col-12 md:col-3">
                    <div class="font-semibold">{{ client.clientName }}</div>
                    <div class="text-500 text-sm">{{ client.clientEmail }}</div>
                  </div>
                  <div class="col-12 md:col-3">
                    <div class="text-500 text-sm">Client Type</div>
                    <div>{{ client.clientType }}</div>
                  </div>
                  <div class="col-12 md:col-3">
                    <div class="text-500 text-sm">Racing Australia Tier</div>
                    <div>{{ client.racingAustraliaTier }}</div>
                  </div>
                  <div class="col-12 md:col-3">
                    <div class="text-500 text-sm">Mediality Racing Tier</div>
                    <div>{{ client.medialityRacingTier }}</div>
                  </div>
                </div>

                <!-- Racing Australia Data with Pricing -->
                <div v-if="client.racingAustraliaData.length > 0" class="mb-4">
                  <h6 class="text-orange-600 mb-3">
                    <i class="pi pi-flag mr-2"></i>Racing Australia Usage
                  </h6>
                  <div class="grid">
                    <!-- Usage Table -->
                    <div class="col-12 lg:col-7">
                      <DataTable
                        :value="client.racingAustraliaData"
                        class="p-datatable-sm"
                        :showGridlines="true"
                      >
                        <Column
                          field="dataType"
                          header="Data Type"
                          style="width: 35%"
                        >
                          <template #body="slotProps">
                            <div class="font-medium">
                              {{ slotProps.data.dataType }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          header="Metro Sat/Sun"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center font-semibold text-blue-600">
                              {{ slotProps.data.metroSatSun || "" }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          header="Metro Midweek"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center font-semibold text-blue-600">
                              {{ slotProps.data.metroMidweek || "" }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          header="Provincial"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center font-semibold text-blue-600">
                              {{ slotProps.data.provincial || "" }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          header="Country"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center font-semibold text-blue-600">
                              {{ slotProps.data.country || "" }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          header="Total"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center font-bold text-green-600">
                              {{ slotProps.data.total || 0 }}
                            </div>
                          </template>
                        </Column>
                      </DataTable>
                    </div>
                    
                    <!-- Pricing Table -->
                    <div class="col-12 lg:col-5">
                      <h6 class="text-sm font-semibold mb-2 text-600">
                        <i class="pi pi-dollar mr-1"></i>Pricing (AUD per month)
                      </h6>
                      <DataTable
                        :value="getRacingAustraliaPricing(client.clientEmail, client.racingAustraliaData)"
                        class="p-datatable-sm"
                        :showGridlines="true"
                      >
                        <Column
                          field="dataType"
                          header="Data Type"
                          style="width: 35%"
                        >
                          <template #body="slotProps">
                            <div class="font-medium text-sm">
                              {{ slotProps.data.dataType }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          field="metroSatSun"
                          header="Metro S/S"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center text-sm">
                              {{ slotProps.data.metroSatSun ? '
                  </div>
                </div>

                <!-- Mediality Racing Data -->
                <div v-if="client.medialityRacingData.length > 0" class="mb-4">
                  <h6 class="text-purple-600 mb-3">
                    <i class="pi pi-video mr-2"></i>Mediality Racing Usage
                  </h6>
                  <DataTable
                    :value="client.medialityRacingData"
                    class="p-datatable-sm"
                    tableStyle="min-width: 50rem"
                    :showGridlines="true"
                  >
                    <Column
                      field="dataType"
                      header="Data Type"
                      style="width: 25%"
                    >
                      <template #body="slotProps">
                        <div class="font-medium">
                          {{ slotProps.data.dataType }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Metro Sat/Sun"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.metroSatSun || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Metro Midweek"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.metroMidweek || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Provincial"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.provincial || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Country"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.country || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Total"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-bold text-green-600">
                          {{ slotProps.data.total || 0 }}
                        </div>
                      </template>
                    </Column>
                  </DataTable>
                </div>

                <Divider
                  v-if="
                    client !==
                    processedClientData[processedClientData.length - 1]
                  "
                />
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Details">
            <DataTable
              :value="reportData.details"
              :paginator="true"
              :rows="10"
              :rowsPerPageOptions="[5, 10, 25, 50]"
              tableStyle="min-width: 50rem"
            >
              <Column
                field="client_name"
                header="Client Name"
                sortable
              ></Column>
              <Column field="contact_email" header="Email" sortable></Column>
              <Column field="price_id" header="Price ID" sortable>
                <template #body="slotProps">
                  <Badge
                    :value="slotProps.data.price_id"
                    :severity="
                      slotProps.data.price_id.startsWith('R')
                        ? 'warning'
                        : slotProps.data.price_id.startsWith('M')
                        ? 'info'
                        : 'secondary'
                    "
                  />
                </template>
              </Column>
              <Column field="decoded_info" header="Decoded Info">
                <template #body="slotProps">
                  <div class="text-sm">
                    <div>
                      <strong>Data Type:</strong>
                      {{ decodePriceId(slotProps.data.price_id).dataType }}
                    </div>
                    <div>
                      <strong>Location:</strong>
                      {{ decodePriceId(slotProps.data.price_id).meetLocation }}
                    </div>
                    <div v-if="decodePriceId(slotProps.data.price_id).category">
                      <strong>Category:</strong>
                      {{ decodePriceId(slotProps.data.price_id).category }}
                    </div>
                  </div>
                </template>
              </Column>
              <Column field="meeting_date" header="Meeting Date" sortable>
                <template #body="slotProps">
                  {{ formatDate(slotProps.data.meeting_date) }}
                </template>
              </Column>
              <Column field="count" header="Count" sortable></Column>
            </DataTable>
          </TabPanel>
        </TabView>

        <div
          v-if="!reportData && !loading"
          class="flex justify-content-center align-items-center p-5"
        >
          <div class="text-center">
            <i class="pi pi-chart-bar text-primary" style="font-size: 3rem"></i>
            <h5>No Report Data</h5>
            <p>
              Select a date range and generate a report to view client usage
              data.
            </p>
          </div>
        </div>

        <div
          v-if="loading"
          class="flex justify-content-center align-items-center p-5"
        >
          <ProgressSpinner
            style="width: 50px; height: 50px"
            strokeWidth="8"
            fill="var(--surface-ground)"
            animationDuration=".5s"
          />
        </div>
      </div>
    </div>
  </div>

  <Toast />
</template>

<script>
import axios from "axios";
import { API } from "aws-amplify";
import Badge from "primevue/badge";
import Button from "primevue/button";
import Calendar from "primevue/calendar";
import Column from "primevue/column";
import DataTable from "primevue/datatable";
import Divider from "primevue/divider";
import Dropdown from "primevue/dropdown";
import ProgressSpinner from "primevue/progressspinner";
import TabPanel from "primevue/tabpanel";
import TabView from "primevue/tabview";
import Toast from "primevue/toast";

export default {
  components: {
    Badge,
    Button,
    Calendar,
    Column,
    DataTable,
    Divider,
    Dropdown,
    ProgressSpinner,
    TabPanel,
    TabView,
    Toast,
  },
  data() {
    return {
      startDate: this.getDefaultStartDate(),
      endDate: new Date(),
      selectedClient: null,
      clients: [],
      reportData: null,
      loading: false,
      priceIdMappings: {
        // Data Source Price Model
        dataSourceModel: {
          RW: "Racing Australia, Wholesale",
          RR: "Racing Australia, Retail",
          MW: "Mediality Racing, Wholesale",
          MR: "Mediality Racing, Retail",
        },
        // Data Type - Racing Australia
        racingAustraliaDataType: {
          N: "Fields @ Nominations",
          W: "Fields @ Weights",
          A: "Fields @ Acceptances",
          R: "Results",
        },
        // Data Type - Mediality Racing
        medialityRacingDataType: {
          C: "Comments",
          T: "Ratings",
          S: "Jockey Silks",
          F: "Form",
        },
        // Customer Tier - Racing Australia
        racingAustraliaTier: {
          A: "Above $100mil",
          B: "$50mil - $100mil",
          C: "$25mil - $50mil",
          D: "$10mil - $25mil",
          E: "$2mil - $10mil",
          F: "Below $2mil",
          G: "Personal Use",
        },
        // Customer Tier - Mediality Racing
        medialityRacingTier: {
          1: "Tier 1",
          2: "Tier 2",
          P: "Personal Use",
          W: "Wholesale",
        },
        // Meet Location
        meetLocation: {
          S: "Metro Sat/Sun",
          M: "Metro Midweek",
          P: "Provincial",
          C: "Country",
        },
        // Category
        category: {
          T: "TAB",
          N: "Non-TAB",
        },
      },
      // Pricing data mapped from the CSV
      pricingData: {
        "RW-N-A-S-T": 16.80,
        "RW-N-A-M-T": 10.92,
        "RW-N-A-P-T": 7.56,
        "RW-N-A-C-T": 5.04,
        "RW-W-A-S-T": 16.80,
        "RW-W-A-M-T": 10.92,
        "RW-W-A-P-T": 7.56,
        "RW-W-A-C-T": 5.04,
        "RW-A-A-S-T": 16.80,
        "RW-A-A-M-T": 10.92,
        "RW-A-A-P-T": 7.56,
        "RW-A-A-C-T": 5.04,
        "RW-R-A-S-T": 28.00,
        "RW-R-A-M-T": 18.20,
        "RW-R-A-P-T": 12.60,
        "RW-R-A-C-T": 8.40,
        "RW-N-A-S-N": 8.40,
        "RW-N-A-M-N": 5.46,
        "RW-N-A-P-N": 3.78,
        "RW-N-A-C-N": 2.52,
        "RW-W-A-S-N": 8.40,
        "RW-W-A-M-N": 5.46,
        "RW-W-A-P-N": 3.78,
        "RW-W-A-C-N": 2.52,
        "RW-A-A-S-N": 8.40,
        "RW-A-A-M-N": 5.46,
        "RW-A-A-P-N": 3.78,
        "RW-A-A-C-N": 2.52,
        "RW-R-A-S-N": 14.00,
        "RW-R-A-M-N": 9.10,
        "RW-R-A-P-N": 6.30,
        "RW-R-A-C-N": 4.20,
        "RW-N-B-S-T": 14.28,
        "RW-N-B-M-T": 9.28,
        "RW-N-B-P-T": 6.43,
        "RW-N-B-C-T": 4.28,
        "RW-W-B-S-T": 14.28,
        "RW-W-B-M-T": 9.28,
        "RW-W-B-P-T": 6.43,
        "RW-W-B-C-T": 4.28,
        "RW-A-B-S-T": 14.28,
        "RW-A-B-M-T": 9.28,
        "RW-A-B-P-T": 6.43,
        "RW-A-B-C-T": 4.28,
        "RW-R-B-S-T": 23.80,
        "RW-R-B-M-T": 15.47,
        "RW-R-B-P-T": 10.71,
        "RW-R-B-C-T": 7.14,
        "RW-N-B-S-N": 7.14,
        "RW-N-B-M-N": 4.65,
        "RW-N-B-P-N": 3.22,
        "RW-N-B-C-N": 2.14,
        "RW-W-B-S-N": 7.14,
        "RW-W-B-M-N": 4.65,
        "RW-W-B-P-N": 3.22,
        "RW-W-B-C-N": 2.14,
        "RW-A-B-S-N": 7.14,
        "RW-A-B-M-N": 4.65,
        "RW-A-B-P-N": 3.22,
        "RW-A-B-C-N": 2.14,
        "RW-R-B-S-N": 11.90,
        "RW-R-B-M-N": 7.74,
        "RW-R-B-P-N": 5.36,
        "RW-R-B-C-N": 3.57,
        "RW-N-C-S-T": 12.60,
        "RW-N-C-M-T": 8.19,
        "RW-N-C-P-T": 5.67,
        "RW-N-C-C-T": 3.78,
        "RW-W-C-S-T": 12.60,
        "RW-W-C-M-T": 8.19,
        "RW-W-C-P-T": 5.67,
        "RW-W-C-C-T": 3.78,
        "RW-A-C-S-T": 12.60,
        "RW-A-C-M-T": 8.19,
        "RW-A-C-P-T": 5.67,
        "RW-A-C-C-T": 3.78,
        "RW-R-C-S-T": 21.00,
        "RW-R-C-M-T": 13.65,
        "RW-R-C-P-T": 9.45,
        "RW-R-C-C-T": 6.30,
        "RW-N-C-S-N": 6.30,
        "RW-N-C-M-N": 4.10,
        "RW-N-C-P-N": 2.84,
        "RW-N-C-C-N": 1.89,
        "RW-W-C-S-N": 6.30,
        "RW-W-C-M-N": 4.10,
        "RW-W-C-P-N": 2.84,
        "RW-W-C-C-N": 1.89,
        "RW-A-C-S-N": 6.30,
        "RW-A-C-M-N": 4.10,
        "RW-A-C-P-N": 2.84,
        "RW-A-C-C-N": 1.89,
        "RW-R-C-S-N": 10.50,
        "RW-R-C-M-N": 6.83,
        "RW-R-C-P-N": 4.73,
        "RW-R-C-C-N": 3.15,
        "RW-N-D-S-T": 10.08,
        "RW-N-D-M-T": 6.55,
        "RW-N-D-P-T": 4.54,
        "RW-N-D-C-T": 3.02,
        "RW-W-D-S-T": 10.08,
        "RW-W-D-M-T": 6.55,
        "RW-W-D-P-T": 4.54,
        "RW-W-D-C-T": 3.02,
        "RW-A-D-S-T": 10.08,
        "RW-A-D-M-T": 6.55,
        "RW-A-D-P-T": 4.54,
        "RW-A-D-C-T": 3.02,
        "RW-R-D-S-T": 16.80,
        "RW-R-D-M-T": 10.92,
        "RW-R-D-P-T": 7.56,
        "RW-R-D-C-T": 5.04,
        "RW-N-D-S-N": 5.04,
        "RW-N-D-M-N": 3.28,
        "RW-N-D-P-N": 2.27,
        "RW-N-D-C-N": 1.51,
        "RW-W-D-S-N": 5.04,
        "RW-W-D-M-N": 3.28,
        "RW-W-D-P-N": 2.27,
        "RW-W-D-C-N": 1.51,
        "RW-A-D-S-N": 5.04,
        "RW-A-D-M-N": 3.28,
        "RW-A-D-P-N": 2.27,
        "RW-A-D-C-N": 1.51,
        "RW-R-D-S-N": 8.40,
        "RW-R-D-M-N": 5.46,
        "RW-R-D-P-N": 3.78,
        "RW-R-D-C-N": 2.52,
        "RW-N-E-S-T": 6.72,
        "RW-N-E-M-T": 4.37,
        "RW-N-E-P-T": 3.02,
        "RW-N-E-C-T": 2.02,
        "RW-W-E-S-T": 6.72,
        "RW-W-E-M-T": 4.37,
        "RW-W-E-P-T": 3.02,
        "RW-W-E-C-T": 2.02,
        "RW-A-E-S-T": 6.72,
        "RW-A-E-M-T": 4.37,
        "RW-A-E-P-T": 3.02,
        "RW-A-E-C-T": 2.02,
        "RW-R-E-S-T": 11.20,
        "RW-R-E-M-T": 7.28,
        "RW-R-E-P-T": 5.04,
        "RW-R-E-C-T": 3.36,
        "RW-N-E-S-N": 3.36,
        "RW-N-E-M-N": 2.18,
        "RW-N-E-P-N": 1.51,
        "RW-N-E-C-N": 1.01,
        "RW-W-E-S-N": 3.36,
        "RW-W-E-M-N": 2.18,
        "RW-W-E-P-N": 1.51,
        "RW-W-E-C-N": 1.01,
        "RW-A-E-S-N": 3.36,
        "RW-A-E-M-N": 2.18,
        "RW-A-E-P-N": 1.51,
        "RW-A-E-C-N": 1.01,
        "RW-R-E-S-N": 5.60,
        "RW-R-E-M-N": 3.64,
        "RW-R-E-P-N": 2.52,
        "RW-R-E-C-N": 1.68,
        "RW-N-F-S-T": 5.04,
        "RW-N-F-M-T": 3.28,
        "RW-N-F-P-T": 2.27,
        "RW-N-F-C-T": 1.51,
        "RW-W-F-S-T": 5.04,
        "RW-W-F-M-T": 3.28,
        "RW-W-F-P-T": 2.27,
        "RW-W-F-C-T": 1.51,
        "RW-A-F-S-T": 5.04,
        "RW-A-F-M-T": 3.28,
        "RW-A-F-P-T": 2.27,
        "RW-A-F-C-T": 1.51,
        "RW-R-F-S-T": 8.40,
        "RW-R-F-M-T": 5.46,
        "RW-R-F-P-T": 3.78,
        "RW-R-F-C-T": 2.52,
        "RW-N-F-S-N": 2.52,
        "RW-N-F-M-N": 1.64,
        "RW-N-F-P-N": 1.13,
        "RW-N-F-C-N": 0.76,
        "RW-W-F-S-N": 2.52,
        "RW-W-F-M-N": 1.64,
        "RW-W-F-P-N": 1.13,
        "RW-W-F-C-N": 0.76,
        "RW-A-F-S-N": 2.52,
        "RW-A-F-M-N": 1.64,
        "RW-A-F-P-N": 1.13,
        "RW-A-F-C-N": 0.76,
        "RW-R-F-S-N": 4.20,
        "RW-R-F-M-N": 2.73,
        "RW-R-F-P-N": 1.89,
        "RW-R-F-C-N": 1.26,
        "RR-N-G-S-T": 3.36,
        "RR-N-G-M-T": 2.18,
        "RR-N-G-P-T": 1.51,
        "RR-N-G-C-T": 1.01,
        "RR-W-G-S-T": 3.36,
        "RR-W-G-M-T": 2.18,
        "RR-W-G-P-T": 1.51,
        "RR-W-G-C-T": 1.01,
        "RR-A-G-S-T": 3.36,
        "RR-A-G-M-T": 2.18,
        "RR-A-G-P-T": 1.51,
        "RR-A-G-C-T": 1.01,
        "RR-R-G-S-T": 5.60,
        "RR-R-G-M-T": 3.64,
        "RR-R-G-P-T": 2.52,
        "RR-R-G-C-T": 1.68,
        "RR-N-G-S-N": 1.68,
        "RR-N-G-M-N": 1.09,
        "RR-N-G-P-N": 0.76,
        "RR-N-G-C-N": 0.50,
        "RR-W-G-S-N": 1.68,
        "RR-W-G-M-N": 1.09,
        "RR-W-G-P-N": 0.76,
        "RR-W-G-C-N": 0.50,
        "RR-A-G-S-N": 1.68,
        "RR-A-G-M-N": 1.09,
        "RR-A-G-P-N": 0.76,
        "RR-A-G-C-N": 0.50,
        "RR-R-G-S-N": 2.80,
        "RR-R-G-M-N": 1.82,
        "RR-R-G-P-N": 1.26,
        "RR-R-G-C-N": 0.84,
      },
    };
  },
  computed: {
    racingAustraliaUsage() {
      return (
        this.reportData?.summary?.business_types?.racing_australia
          ?.total_count || 0
      );
    },
    medialityRacingUsage() {
      return (
        this.reportData?.summary?.business_types?.mediality_racing
          ?.total_count || 0
      );
    },
    processedClientData() {
      if (!this.reportData || !this.reportData.client_summary) return [];

      return Object.entries(this.reportData.client_summary).map(
        ([email, data]) => {
          const racingAustraliaData = this.processRacingAustraliaData(
            data.racing_australia.price_ids
          );
          const medialityRacingData = this.processMedialityRacingData(
            data.mediality_racing.price_ids
          );

          return {
            clientEmail: email,
            clientName: data.client_name,
            clientType: data.client_type,
            racingAustraliaTier: this.getTierDescription(
              data.racing_australia.pricing_tier,
              "racing"
            ),
            medialityRacingTier: this.getTierDescription(
              data.mediality_racing.pricing_tier,
              "mediality"
            ),
            racingAustraliaData,
            medialityRacingData,
          };
        }
      );
    },
    reportURL() {
      return this.isAdmin() ? "https://4uls0hqk4d.execute-api.ap-southeast-2.amazonaws.com/report" : "https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report";
    },
  },
  mounted() {
    this.fetchClients();
  },
  methods: {
    getDefaultStartDate() {
      const d = new Date();
      d.setDate(d.getDate() - 30);
      return d;
    },
    isoDate(d) {
      return d ? d.toISOString().split("T")[0] : "";
    },
    formatDate(d) {
      return d
        ? new Date(d).toLocaleDateString("en-AU", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
          })
        : "N/A";
    },
    formatDateRange() {
      return `${this.formatDate(this.startDate)} - ${this.formatDate(
        this.endDate
      )}`;
    },

    getTierDescription(tier, businessType) {
      if (businessType === "racing") {
        return (
          this.priceIdMappings.racingAustraliaTier[tier] || tier || "WHOLESALE"
        );
      } else {
        return (
          this.priceIdMappings.medialityRacingTier[tier] || tier || "WHOLESALE"
        );
      }
    },

    getClientTier(clientEmail) {
      if (!this.reportData || !this.reportData.client_summary) return null;
      const clientData = this.reportData.client_summary[clientEmail];
      return clientData?.racing_australia?.pricing_tier || null;
    },

    getPriceForPriceId(priceId) {
      // Remove the -AUD-1M suffix if present
      const cleanPriceId = priceId.replace(/-AUD-1M$/, '');
      return this.pricingData[cleanPriceId] || null;
    },

    getRacingAustraliaPricing(clientEmail, racingAustraliaData) {
      const tier = this.getClientTier(clientEmail);
      if (!tier) return [];

      console.log(`Client ${clientEmail} has tier: ${tier}`);
      console.log('Racing Australia Data:', racingAustraliaData);

      const pricingRows = [];
      
      racingAustraliaData.forEach(row => {
        const pricingRow = {
          dataType: row.dataType,
          metroSatSun: null,
          metroMidweek: null,
          provincial: null,
          country: null,
          total: 0,
        };

        // Extract data type code and category from the dataType
        let dataTypeCode = null;
        let categoryCode = 'T'; // Default to TAB
        
        // Check category first
        if (row.dataType.includes('Non-TAB')) {
          categoryCode = 'N';
        } else if (row.dataType.includes('TAB')) {
          categoryCode = 'T';
        }
        
        // Map full data type names to codes
        if (row.dataType.includes('Fields @ Nominations')) {
          dataTypeCode = 'N';
        } else if (row.dataType.includes('Fields @ Weights')) {
          dataTypeCode = 'W';
        } else if (row.dataType.includes('Fields @ Acceptances')) {
          dataTypeCode = 'A';
        } else if (row.dataType.includes('Results')) {
          dataTypeCode = 'R';
        }

        console.log(`Processing ${row.dataType}: dataTypeCode=${dataTypeCode}, categoryCode=${categoryCode}`);

        if (dataTypeCode) {
          // Get prices for each location
          if (row.metroSatSun > 0) {
            const priceId = `RW-${dataTypeCode}-${tier}-S-${categoryCode}`;
            const price = this.getPriceForPriceId(priceId);
            if (price) {
              pricingRow.metroSatSun = price;
              const cost = price * row.metroSatSun;
              pricingRow.total += cost;
              console.log(`Metro Sat/Sun: ${row.metroSatSun} units × ${price} = ${cost}`);
            }
          }
          if (row.metroMidweek > 0) {
            const priceId = `RW-${dataTypeCode}-${tier}-M-${categoryCode}`;
            const price = this.getPriceForPriceId(priceId);
            if (price) {
              pricingRow.metroMidweek = price;
              const cost = price * row.metroMidweek;
              pricingRow.total += cost;
              console.log(`Metro Midweek: ${row.metroMidweek} units × ${price} = ${cost}`);
            }
          }
          if (row.provincial > 0) {
            const priceId = `RW-${dataTypeCode}-${tier}-P-${categoryCode}`;
            const price = this.getPriceForPriceId(priceId);
            if (price) {
              pricingRow.provincial = price;
              const cost = price * row.provincial;
              pricingRow.total += cost;
              console.log(`Provincial: ${row.provincial} units × ${price} = ${cost}`);
            }
          }
          if (row.country > 0) {
            const priceId = `RW-${dataTypeCode}-${tier}-C-${categoryCode}`;
            const price = this.getPriceForPriceId(priceId);
            if (price) {
              pricingRow.country = price;
              const cost = price * row.country;
              pricingRow.total += cost;
              console.log(`Country: ${row.country} units × ${price} = ${cost}`);
            }
          }
          
          // Round total to 2 decimal places
          pricingRow.total = Math.round(pricingRow.total * 100) / 100;
          console.log(`Total for ${row.dataType}: ${pricingRow.total}`);
        }

        pricingRows.push(pricingRow);
      });

      return pricingRows;
    },

    decodePriceId(priceId) {
      if (!priceId)
        return { dataType: "Unknown", meetLocation: "Unknown", category: "" };

      const parts = priceId.split("-");
      if (parts.length < 4)
        return { dataType: "Unknown", meetLocation: "Unknown", category: "" };

      const [sourceModel, dataTypeCode, _tier, meetLocationCode, categoryCode] =
        parts;
      console.log(_tier);
      let dataType = "Unknown";
      if (sourceModel.startsWith("R")) {
        dataType =
          this.priceIdMappings.racingAustraliaDataType[dataTypeCode] ||
          dataTypeCode;
      } else if (sourceModel.startsWith("M")) {
        dataType =
          this.priceIdMappings.medialityRacingDataType[dataTypeCode] ||
          dataTypeCode;
      }

      const meetLocation =
        this.priceIdMappings.meetLocation[meetLocationCode] || meetLocationCode;
      const category = categoryCode
        ? this.priceIdMappings.category[categoryCode] || categoryCode
        : "";

      return { dataType, meetLocation, category };
    },

    processRacingAustraliaData(priceIds) {
      const dataTypes = {};

      // Group by data type and category
      Object.entries(priceIds || {}).forEach(([priceId, count]) => {
        const decoded = this.decodePriceId(priceId);
        const parts = priceId.split("-");

        if (parts.length >= 5) {
          const [, _dataTypeCode, , meetLocationCode, categoryCode] = parts;
          const category = this.priceIdMappings.category[categoryCode] || "";
          const dataTypeKey = `${category} ${decoded.dataType}`.trim();
          console.log(_dataTypeCode);
          if (!dataTypes[dataTypeKey]) {
            dataTypes[dataTypeKey] = {
              dataType: dataTypeKey,
              metroSatSun: 0,
              metroMidweek: 0,
              provincial: 0,
              country: 0,
              total: 0,
            };
          }

          // Map meet location to columns
          switch (meetLocationCode) {
            case "S":
              dataTypes[dataTypeKey].metroSatSun += count;
              break;
            case "M":
              dataTypes[dataTypeKey].metroMidweek += count;
              break;
            case "P":
              dataTypes[dataTypeKey].provincial += count;
              break;
            case "C":
              dataTypes[dataTypeKey].country += count;
              break;
          }
          dataTypes[dataTypeKey].total += count;
        }
      });

      return Object.values(dataTypes).filter((item) => item.total > 0);
    },

    processMedialityRacingData(priceIds) {
      const dataTypes = {};

      // Group by data type and category
      Object.entries(priceIds || {}).forEach(([priceId, count]) => {
        const decoded = this.decodePriceId(priceId);
        const parts = priceId.split("-");

        if (parts.length >= 5) {
          const [, , , meetLocationCode, categoryCode] = parts;
          const category = this.priceIdMappings.category[categoryCode] || "";
          const dataTypeKey = `${category} ${decoded.dataType}`.trim();

          if (!dataTypes[dataTypeKey]) {
            dataTypes[dataTypeKey] = {
              dataType: dataTypeKey,
              metroSatSun: 0,
              metroMidweek: 0,
              provincial: 0,
              country: 0,
              total: 0,
              priceIds: [],
            };
          }

          // Map meet location to columns
          switch (meetLocationCode) {
            case "S":
              dataTypes[dataTypeKey].metroSatSun += count;
              break;
            case "M":
              dataTypes[dataTypeKey].metroMidweek += count;
              break;
            case "P":
              dataTypes[dataTypeKey].provincial += count;
              break;
            case "C":
              dataTypes[dataTypeKey].country += count;
              break;
          }
          dataTypes[dataTypeKey].total += count;
          dataTypes[dataTypeKey].priceIds.push(priceId);
        }
      });

      return Object.values(dataTypes).filter((item) => item.total > 0);
    },

    async fetchClients() {
      try {
        const response = await API.get("MrCenApiGateway", "/admin/client", {});
        this.clients = response.map((client) => ({
          id: client._id,
          name: client.account_holder,
          email: client.email,
          customerID: client.chargebee_customer_id,
        }));
      } catch (error) {
        console.error("Error fetching clients:", error);
        this.$toast.add({
          severity: "error",
          summary: "Error",
          detail: "Failed to fetch clients",
          life: 3000,
        });
      }
    },
    isAdmin() {
      return window.location.href.startsWith(
        "https://www.admin.medialityracing.com.au/"
      );
    },
    generateReport() {
      const body = {
        startDate: this.isoDate(this.startDate),
        endDate: this.isoDate(this.endDate),
        markAsRecorded: false,
      };
      if (this.selectedClient) body.clientEmail = this.selectedClient.email;

      this.loading = true;
      axios
        .post(
          this.reportURL,
          body,
          { headers: { "Content-Type": "application/json" } }
        )
        .then((res) => {
          this.reportData = res.data;
          this.$toast.add({
            severity: "success",
            summary: "Report Ready",
            detail: `Report ${this.formatDate(
              this.startDate
            )} – ${this.formatDate(this.endDate)} generated.`,
            life: 4000,
          });
        })
        .catch((err) => {
          console.error("Generate report failed:", err);
          this.$toast.add({
            severity: "error",
            summary: "Error",
            detail: "Could not generate the report. See console for details.",
            life: 5000,
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },

    downloadRacingAustraliaCSV() {
      if (!this.reportData) return;

      let csv = "Racing Australia Usage Report\n";
      csv += `Date Range: ${this.formatDateRange()}\n\n`;

      this.processedClientData.forEach((client) => {
        // Only include clients with Racing Australia data
        if (client.racingAustraliaData.length > 0) {
          csv += `Client Name: ${client.clientName}\n`;
          csv += `Client Type: ${client.clientType}\n`;
          csv += `Racing Australia Tier: ${client.racingAustraliaTier}\n\n`;

          csv += "Racing Australia Usage:\n";
          csv +=
            "Data Type,Metro Sat/Sun,Metro Midweek,Provincial,Country,Total\n";
          client.racingAustraliaData.forEach((row) => {
            csv += `"${row.dataType}",${row.metroSatSun || 0},${
              row.metroMidweek || 0
            },${row.provincial || 0},${row.country || 0},${row.total}\n`;
          });
          
          // Add pricing information
          const pricingData = this.getRacingAustraliaPricing(client.clientEmail, client.racingAustraliaData);
          csv += "\nRacing Australia Pricing (AUD per month):\n";
          csv += "Data Type,Metro Sat/Sun,Metro Midweek,Provincial,Country,Total\n";
          pricingData.forEach((row) => {
          csv += `"${row.dataType}",${row.metroSatSun || 0},${
              row.metroMidweek || 0
            },${row.provincial || 0},${row.country || 0},${row.total}\n`;
      });
        });
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `racing-australia-report-${this.isoDate(
          this.startDate
        )}-to-${this.isoDate(this.endDate)}.csv`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$toast.add({
        severity: "info",
        summary: "Download Started",
        detail: "Your Racing Australia CSV file is being downloaded",
        life: 3000,
      });
    }
  },
    downloadMedialityRacingCSV() {
      if (!this.reportData) return;

      let csv = "Mediality Racing Usage Report\n";
      csv += `Date Range: ${this.formatDateRange()}\n\n`;

      this.processedClientData.forEach((client) => {
        // Only include clients with Mediality Racing data
        if (client.medialityRacingData.length > 0) {
          csv += `Client Name: ${client.clientName}\n`;
          csv += `Client Type: ${client.clientType}\n`;
          csv += `Mediality Racing Tier: ${client.medialityRacingTier}\n\n`;

          csv += "Mediality Racing Usage:\n";
          csv +=
            "Data Type,Metro Sat/Sun,Metro Midweek,Provincial,Country,Total\n";
          client.medialityRacingData.forEach((row) => {
            csv += `"${row.dataType}",${row.metroSatSun || 0},${
              row.metroMidweek || 0
            },${row.provincial || 0},${row.country || 0},${row.total}\n`;
          });
          csv += "\n---\n\n";
        }
      });

      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `mediality-racing-report-${this.isoDate(
          this.startDate
        )}-to-${this.isoDate(this.endDate)}.csv`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$toast.add({
        severity: "info",
        summary: "Download Started",
        detail: "Your Mediality Racing CSV file is being downloaded",
        life: 3000,
      });
    },
  },
};
</script>

<style scoped>
.p-dropdown {
  width: 100%;
}

.p-datatable .p-datatable-thead > tr > th {
  background-color: var(--surface-200);
  font-weight: 600;
}

/* Ensure proper spacing for side-by-side tables */
.grid .col-12.lg\:col-7 {
  padding-right: 0.5rem;
}

.grid .col-12.lg\:col-5 {
  padding-left: 0.5rem;
}

/* Responsive design for smaller screens */
@media (max-width: 992px) {
  .grid .col-12.lg\:col-7,
  .grid .col-12.lg\:col-5 {
    padding-right: 0;
    padding-left: 0;
  }
}
</style> + slotProps.data.metroSatSun : '-' }}
                            </div>
                          </template>
                        </Column>
                        <Column
                          field="metroMidweek"
                          header="Metro M/W"
                          style="width: 13%"
                          headerClass="text-center"
                        >
                          <template #body="slotProps">
                            <div class="text-center text-sm">
                              {{ slotProps.data.metroMidweek ? '
                  </div>
                </div>

                <!-- Mediality Racing Data -->
                <div v-if="client.medialityRacingData.length > 0" class="mb-4">
                  <h6 class="text-purple-600 mb-3">
                    <i class="pi pi-video mr-2"></i>Mediality Racing Usage
                  </h6>
                  <DataTable
                    :value="client.medialityRacingData"
                    class="p-datatable-sm"
                    tableStyle="min-width: 50rem"
                    :showGridlines="true"
                  >
                    <Column
                      field="dataType"
                      header="Data Type"
                      style="width: 25%"
                    >
                      <template #body="slotProps">
                        <div class="font-medium">
                          {{ slotProps.data.dataType }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Metro Sat/Sun"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.metroSatSun || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Metro Midweek"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.metroMidweek || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Provincial"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.provincial || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Country"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-semibold text-purple-600">
                          {{ slotProps.data.country || "" }}
                        </div>
                      </template>
                    </Column>
                    <Column
                      header="Total"
                      style="width: 15%"
                      headerClass="text-center"
                    >
                      <template #body="slotProps">
                        <div class="text-center font-bold text-green-600">
                          {{ slotProps.data.total || 0 }}
                        </div>
                      </template>
                    </Column>
                  </DataTable>
                </div>

                <Divider
                  v-if="
                    client !==
                    processedClientData[processedClientData.length - 1]
                  "
                />
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Details">
            <DataTable
              :value="reportData.details"
              :paginator="true"
              :rows="10"
              :rowsPerPageOptions="[5, 10, 25, 50]"
              tableStyle="min-width: 50rem"
            >
              <Column
                field="client_name"
                header="Client Name"
                sortable
              ></Column>
              <Column field="contact_email" header="Email" sortable></Column>
              <Column field="price_id" header="Price ID" sortable>
                <template #body="slotProps">
                  <Badge
                    :value="slotProps.data.price_id"
                    :severity="
                      slotProps.data.price_id.startsWith('R')
                        ? 'warning'
                        : slotProps.data.price_id.startsWith('M')
                        ? 'info'
                        : 'secondary'
                    "
                  />
                </template>
              </Column>
              <Column field="decoded_info" header="Decoded Info">
                <template #body="slotProps">
                  <div class="text-sm">
                    <div>
                      <strong>Data Type:</strong>
                      {{ decodePriceId(slotProps.data.price_id).dataType }}
                    </div>
                    <div>
                      <strong>Location:</strong>
                      {{ decodePriceId(slotProps.data.price_id).meetLocation }}
                    </div>
                    <div v-if="decodePriceId(slotProps.data.price_id).category">
                      <strong>Category:</strong>
                      {{ decodePriceId(slotProps.data.price_id).category }}
                    </div>
                  </div>
                </template>
              </Column>
              <Column field="meeting_date" header="Meeting Date" sortable>
                <template #body="slotProps">
                  {{ formatDate(slotProps.data.meeting_date) }}
                </template>
              </Column>
              <Column field="count" header="Count" sortable></Column>
            </DataTable>
          </TabPanel>
        </TabView>

        <div
          v-if="!reportData && !loading"
          class="flex justify-content-center align-items-center p-5"
        >
          <div class="text-center">
            <i class="pi pi-chart-bar text-primary" style="font-size: 3rem"></i>
            <h5>No Report Data</h5>
            <p>
              Select a date range and generate a report to view client usage
              data.
            </p>
          </div>
        </div>

        <div
          v-if="loading"
          class="flex justify-content-center align-items-center p-5"
        >
          <ProgressSpinner
            style="width: 50px; height: 50px"
            strokeWidth="8"
            fill="var(--surface-ground)"
            animationDuration=".5s"
          />
        </div>
      </div>
    </div>
  </div>

  <Toast />
</template>

<script>